services:
  web-app:
    image: ${DOCKER_USERNAME}/${APP_NAME}:latest

    env_file:
      - .env.production
    labels:
      - traefik.enable=true
      - traefik.http.routers.web-app.rule=Host(`www.coloraria.com`) || Host(`coloraria.com`)
      - traefik.http.routers.web-app.entrypoints=https
      - traefik.http.routers.web-app.tls=true
      - traefik.http.routers.web-app.tls.certresolver=letsencrypt
      - traefik.http.services.web-app.loadbalancer.server.port=80
    networks:
      - traefik-net

networks:
  traefik-net:
    external: true
