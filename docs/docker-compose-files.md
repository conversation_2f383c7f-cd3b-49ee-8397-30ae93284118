## For dev
docker-compose.yml: postgres + app, this do not run next build, suitable for dev with hot-reload

## Local build test
Build image with buildx
1. Create network
2. Start postgres
3. Create buildx builder attach to the same network
4. Bootstrap buildx builder
5. Build image with buildx

## For CI
Same as above.

## For CD
Build image is same as above.
Additional steps:
1. Tag image with semantic version
2. Push image to registry

## For production (ideally none of following services should stop running)
docker-compose.postgres.yml: start postgres service
docker-compose.traefik.yml: start reverse proxy server
docker-compose.prod.yml: start the main application
