# /// script
# requires-python = ">=3.12"
# dependencies = [
#     "google-api-python-client",
#     "google-auth-httplib2",
#     "google-auth-oauthlib",
# ]
# ///

from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload
import google.auth

# If modifying these scopes, delete the file token.json.
SCOPES = ["https://www.googleapis.com/auth/drive.file"]
LOCAL_FILE_PATH = 'test.txt'  # <-- IMPORTANT: Create this file in the same directory.
DRIVE_FILE_NAME = 'MyTestUpload.txt'
# TOKEN_FILE = "coloraria-7be8d983310e.json"


def main():
  """Shows basic usage of the Drive v3 API.
  Prints the names and ids of the first 10 files the user has access to.
  """
  credentials, _ = google.auth.default(scopes=SCOPES)
  service = build('drive', 'v3', credentials=credentials)

  try:
    # Call the Drive v3 API
    results = (
        service.files()
        .list(pageSize=10, fields="nextPageToken, files(id, name)")
        .execute()
    )
    items = results.get("files", [])

    if not items:
      print("No files found.")
      return
    print("Files:")
    for item in items:
      print(f"{item['name']} ({item['id']})")



    # a. Create the metadata for the file.
    file_metadata = {
        'name': DRIVE_FILE_NAME
        # Uncomment the line below to upload to a specific folder.
        # 'parents': [FOLDER_ID] 
    }
    
    # b. Create the MediaFileUpload object with the file's content and MIME type.
    # The client library will automatically detect the MIME type if you don't specify it.
    media = MediaFileUpload(LOCAL_FILE_PATH, resumable=True)

    # c. Call the files.create method to upload the file.
    # - body: The file's metadata.
    # - media_body: The file's content.
    # - fields: Specifies which fields to return in the response. 'id' is all we need.
    file = service.files().create(
        body=file_metadata,
        media_body=media,
        fields='id'
    ).execute()

    print(f"✅ File uploaded successfully!")
    print(f"File Name: {DRIVE_FILE_NAME}")
    print(f"File ID: {file.get('id')}")


    results = (
        service.files()
        .list(pageSize=10, fields="nextPageToken, files(id, name)")
        .execute()
    )
    items = results.get("files", [])

    if not items:
      print("No files found.")
      return
    print("Files:")
    for item in items:
      print(f"{item['name']} ({item['id']})")


  except HttpError as error:
    print(f"❌ An error occurred during upload: {error}")
  except FileNotFoundError:
    print(f"❌ Error: The file '{LOCAL_FILE_PATH}' was not found.")
  except HttpError as error:
    # TODO(developer) - Handle errors from drive API.
    print(f"An error occurred: {error}")


if __name__ == "__main__":
  main()
